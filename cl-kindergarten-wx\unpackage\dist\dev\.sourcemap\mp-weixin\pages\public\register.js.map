{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/public/register.vue?0b76", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/public/register.vue?4c25", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/public/register.vue?5490", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/public/register.vue?bb4e", "uni-app:///pages/public/register.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/public/register.vue?8aef", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/public/register.vue?fc4a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "action", "fileList", "popupVisible", "protocolsContent", "check", "agreement", "form", "username", "name", "phone", "cardno", "photo", "password", "form_data", "floder", "rules", "required", "message", "trigger", "type", "onReady", "methods", "checkboxChange", "showPopup", "id", "hidePopup", "submit", "setTimeout", "uni", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACiCtnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;MACA;MACAC;QACAR,WACA;UACAS;UACAC;UACA;UACAC;QACA,EACA;QACAN,WACA;UACAI;UACAC;UACA;UACAC;QACA,EACA;QACAV,OACA;UACAQ;UACAC;UACA;UACAC;QACA,EACA;QACAT,QACA;UACAO;UACAG;UACAF;UACA;UACAC;QACA;MAEA;IACA;EACA;EACAE;IACA;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MAAA;MACA;MAEA;QAAAC;MAAA;MACA;QACA;QACA;MACA;IAEA;IACAC;MACA;IACA;IAEAC;MAAA;MACA;QACA;UACA;UACA;YACA;cACA;cACAC;gBACAC;cACA;cAEAC;YACA;cACA;cACAA;YACA;UACA;UAGAA;QACA;UAEAA;QACA;MACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7IA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,mnCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/public/register.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/public/register.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register.vue?vue&type=template&id=33acbfa0&\"\nvar renderjs\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/public/register.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=template&id=33acbfa0&\"", "var components\ntry {\n  components = {\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-checkbox/u-checkbox\" */ \"@/uni_modules/uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<u-form :model=\"form\" ref=\"uForm\" :error-type=\"['toast']\">\r\n\t\t\t\t<u-form-item label=\"登陆帐号:\" prop=\"username\"  label-width=\"170\"><u-input placeholder=\"请输入登陆帐号\" v-model=\"form.username\" /></u-form-item>\r\n\t\t\t\t<u-form-item label=\"真实姓名:\" label-width=\"170\" prop=\"name\"><u-input placeholder=\"请输入真实姓名\" v-model=\"form.name\" /></u-form-item>\r\n\t\t\t\t<u-form-item label=\"手机号:\"  label-width=\"170\" prop=\"phone\"><u-input placeholder=\"请输入输入手机号码\" v-model=\"form.phone\" /></u-form-item>\r\n\t\t\t\t<u-form-item label=\"登陆密码:\"  label-width=\"170\" prop=\"password\"><u-input type=\"password\" placeholder=\"请输入登陆密码\" v-model=\"form.password\" /></u-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"agreement\">\r\n\t\t\t\t\t<u-checkbox size=\"40rpx\" v-model=\"check\" @change=\"checkboxChange\"></u-checkbox>\r\n\t\t\t\t\t<view class=\"agreement-text\">\r\n\t\t\t\t\t\t我已知晓并同意 <text @click=\"showPopup(3)\" style=\"color:cornflowerblue\">《用户服务协议》</text>及<text @click=\"showPopup(4)\" style=\"color:cornflowerblue\">《隐私政策》</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"submit_con\">\r\n\t\t\t\t\t<u-button type=\"primary\" @click=\"submit\">提交注册</u-button>\r\n\t\t\t\t</view>\r\n\t\t\t</u-form>\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t<u-popup v-model=\"popupVisible\" mode=\"bottom\" length=\"60%\" :closeable=\"true\">\r\n\t\t\t      <scroll-view class=\"popup-content\" :scroll-y=\"true\">\r\n\t\t\t        <rich-text :nodes=\"protocolsContent\" style=\"line-height: 50rpx;\"></rich-text>\r\n\t\t\t      </scroll-view>\r\n\t\t\t</u-popup>\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {toast, clearStorageSync, setStorageSync,getStorageSync, useRouter} from '@/utils/utils.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\taction: this.$api_url + '/api/index/upload_cos',\r\n\t\t\t\tfileList:[],\r\n\t\t\t\tpopupVisible: false,\r\n\t\t\t\tprotocolsContent: \"\",\r\n\t\t\t\tcheck: false,\r\n\t\t\t\tagreement: false,\r\n\t\t\t\tform: {\r\n\t\t\t\t\tusername: '',\r\n\t\t\t\t\tname: '',\r\n\t\t\t\t\tphone: '',\r\n\t\t\t\t\tcardno: '',\r\n\t\t\t\t\tphoto:'',\r\n\t\t\t\t\tpassword:''\r\n\t\t\t\t},\r\n\t\t\t\tform_data : {\r\n\t\t\t\t\tfloder : 'photo'\r\n\t\t\t\t},\r\n\t\t\t\trules: {\r\n\t\t\t\t\tusername: [\r\n\t\t\t\t\t\t{ \r\n\t\t\t\t\t\t\trequired: true, \r\n\t\t\t\t\t\t\tmessage: '请输入登陆帐号', \r\n\t\t\t\t\t\t\t// 可以单个或者同时写两个触发验证方式 \r\n\t\t\t\t\t\t\ttrigger: ['change','blur'],\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t],\r\n\t\t\t\t\tpassword: [\r\n\t\t\t\t\t\t{ \r\n\t\t\t\t\t\t\trequired: true, \r\n\t\t\t\t\t\t\tmessage: '请输入密码', \r\n\t\t\t\t\t\t\t// 可以单个或者同时写两个触发验证方式 \r\n\t\t\t\t\t\t\ttrigger: ['change','blur'],\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t],\r\n\t\t\t\t\tname: [\r\n\t\t\t\t\t\t{ \r\n\t\t\t\t\t\t\trequired: true, \r\n\t\t\t\t\t\t\tmessage: '请输入真实姓名', \r\n\t\t\t\t\t\t\t// 可以单个或者同时写两个触发验证方式 \r\n\t\t\t\t\t\t\ttrigger: ['change','blur'],\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t],\r\n\t\t\t\t\tphone: [\r\n\t\t\t\t\t\t{ \r\n\t\t\t\t\t\t\trequired: true, \r\n\t\t\t\t\t\t\ttype:\"number\",\r\n\t\t\t\t\t\t\tmessage: '请输入登陆手机号', \r\n\t\t\t\t\t\t\t// 可以单个或者同时写两个触发验证方式 \r\n\t\t\t\t\t\t\ttrigger: ['change','blur'],\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t],\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tonReady(){\r\n\t\t\tthis.$refs.uForm.setRules(this.rules);\r\n\t\t},\r\n\t\t\r\n\t\tmethods: {\r\n\t\t\tcheckboxChange(e) {\r\n\t\t\t\tthis.agreement = e.value;\r\n\t\t\t},\r\n\t\t\tshowPopup(id) {\r\n\t\t\t    this.popupVisible = true;\r\n\t\t\t\t\r\n\t\t\t\tconst params ={id:id}\r\n\t\t\t\tthis.$api.page(params).then(res => {\r\n\t\t\t\t\t//console.log('list', res.data);\r\n\t\t\t\t\tthis.protocolsContent = res.data.content\r\n\t\t\t\t})\t\r\n\r\n\t\t\t},\r\n\t\t\t hidePopup() {\r\n\t\t\t    this.popupVisible = false;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tsubmit() {\r\n\t\t\t\tthis.$refs.uForm.validate(valid => {\r\n\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\tif(!this.agreement) return toast('请查看并同意相关协议');\r\n\t\t\t\t\t\tthis.$api.register(this.form).then(res => {\r\n\t\t\t\t\t\t\tif(res.code==0){\r\n\t\t\t\t\t\t\t\ttoast(res.msg)\r\n\t\t\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t\t\t uni.navigateBack()\r\n\t\t\t\t\t\t\t\t},1500)\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tconsole.log(res); \r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\ttoast(res.msg)\r\n\t\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconsole.log(this.form)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconsole.log('验证失败'); \r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.popup-content {\r\n\t  padding: 40rpx;\r\n\t  margin-top: 60rpx;\r\n\t  margin-bottom: 20rpx;\r\n\t  width: 90%;\r\n\t  height:100%\r\n\t}\r\n\r\n.content{padding:30rpx}\r\n.agreement {\r\n\tdisplay: flex;\r\n\tmargin: 40rpx 0 0 10rpx;\r\n\r\n\t.agreement-text {\r\n\t\tpadding-left: 0rpx;\r\n\t\tcolor: $u-tips-color;\r\n\t}\r\n}\r\n.submit_con{margin-top: 20rpx;}\r\n</style>\r\n\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699050985\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}