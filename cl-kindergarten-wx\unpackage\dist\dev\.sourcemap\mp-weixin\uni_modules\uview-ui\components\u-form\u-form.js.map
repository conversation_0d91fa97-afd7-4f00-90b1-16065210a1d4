{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form/u-form.vue?3f7b", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form/u-form.vue?d1c2", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form/u-form.vue?29bd", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form/u-form.vue?f5a3", "uni-app:///uni_modules/uview-ui/components/u-form/u-form.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form/u-form.vue?40bb", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form/u-form.vue?5ee2"], "names": ["name", "props", "model", "type", "default", "errorType", "borderBottom", "labelPosition", "labelWidth", "labelAlign", "labelStyle", "provide", "uForm", "data", "rules", "created", "methods", "setRules", "resetFields", "field", "validate", "valid", "errorArr", "resolve"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACKpnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA,gBAeA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;EACA;EACAO;IACA;MACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;QACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;UACAD;YACA;YACA;cACAE;cACAC;YACA;YACA;YACA;cACAC;cACA;cACA;gBACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AChIA;AAAA;AAAA;AAAA;AAAmqC,CAAgB,yoCAAG,EAAC,C;;;;;;;;;;;ACAvrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-form/u-form.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-form.vue?vue&type=template&id=786a592e&scoped=true&\"\nvar renderjs\nimport script from \"./u-form.vue?vue&type=script&lang=js&\"\nexport * from \"./u-form.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-form.vue?vue&type=style&index=0&id=786a592e&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"786a592e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-form/u-form.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-form.vue?vue&type=template&id=786a592e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-form.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-form.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-form\"><slot /></view>\n</template>\n\n<script>\n\t/**\n\t * form 表单\n\t * @description 此组件一般用于表单场景，可以配置Input输入框，Select弹出框，进行表单验证等。\n\t * @tutorial http://uviewui.com/components/form.html\n\t * @property {Object} model 表单数据对象\n\t * @property {Boolean} border-bottom 是否显示表单域的下划线边框\n\t * @property {String} label-position 表单域提示文字的位置，left-左侧，top-上方\n\t * @property {String Number} label-width 提示文字的宽度，单位rpx（默认90）\n\t * @property {Object} label-style lable的样式，对象形式\n\t * @property {String} label-align lable的对齐方式\n\t * @property {Object} rules 通过ref设置，见官网说明\n\t * @property {Array} error-type 错误的提示方式，数组形式，见上方说明(默认['message'])\n\t * @example <u-form :model=\"form\" ref=\"uForm\"></u-form>\n\t */\n\nexport default {\n\tname: 'u-form',\n\tprops: {\n\t\t// 当前form的需要验证字段的集合\n\t\tmodel: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {};\n\t\t\t}\n\t\t},\n\t\t// 验证规则\n\t\t// rules: {\n\t\t// \ttype: [Object, Function, Array],\n\t\t// \tdefault() {\n\t\t// \t\treturn {};\n\t\t// \t}\n\t\t// },\n\t\t// 有错误时的提示方式，message-提示信息，border-如果input设置了边框，变成呈红色，\n\t\t// border-bottom-下边框呈现红色，none-无提示\n\t\terrorType: {\n\t\t\ttype: Array,\n\t\t\tdefault() {\n\t\t\t\treturn ['message', 'toast']\n\t\t\t}\n\t\t},\n\t\t// 是否显示表单域的下划线边框\n\t\tborderBottom: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// label的位置，left-左边，top-上边\n\t\tlabelPosition: {\n\t\t\ttype: String,\n\t\t\tdefault: 'left'\n\t\t},\n\t\t// label的宽度，单位rpx\n\t\tlabelWidth: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 90\n\t\t},\n\t\t// lable字体的对齐方式\n\t\tlabelAlign: {\n\t\t\ttype: String,\n\t\t\tdefault: 'left'\n\t\t},\n\t\t// lable的样式，对象形式\n\t\tlabelStyle: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {}\n\t\t\t}\n\t\t},\n\t},\n\tprovide() {\n\t\treturn {\n\t\t\tuForm: this\n\t\t};\n\t},\n\tdata() {\n\t\treturn {\n\t\t\trules: {}\n\t\t};\n\t},\n\tcreated() {\n\t\t// 存储当前form下的所有u-form-item的实例\n\t\t// 不能定义在data中，否则微信小程序会造成循环引用而报错\n\t\tthis.fields = [];\n\t},\n\tmethods: {\n\t\tsetRules(rules) {\n\t\t\tthis.rules = rules;\n\t\t},\n\t\t// 清空所有u-form-item组件的内容，本质上是调用了u-form-item组件中的resetField()方法\n\t\tresetFields() {\n\t\t\tthis.fields.map(field => {\n\t\t\t\tfield.resetField();\n\t\t\t});\n\t\t},\n\t\t// 校验全部数据\n\t\tvalidate(callback) {\n\t\t\treturn new Promise(resolve => {\n\t\t\t\t// 对所有的u-form-item进行校验\n\t\t\t\tlet valid = true; // 默认通过\n\t\t\t\tlet count = 0; // 用于标记是否检查完毕\n\t\t\t\tlet errorArr = []; // 存放错误信息\n\t\t\t\tthis.fields.map(field => {\n\t\t\t\t\t// 调用每一个u-form-item实例的validation的校验方法\n\t\t\t\t\tfield.validation('', error => {\n\t\t\t\t\t\t// 如果任意一个u-form-item校验不通过，就意味着整个表单不通过\n\t\t\t\t\t\tif (error) {\n\t\t\t\t\t\t\tvalid = false;\n\t\t\t\t\t\t\terrorArr.push(error);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 当历遍了所有的u-form-item时，调用promise的then方法\n\t\t\t\t\t\tif (++count === this.fields.length) {\n\t\t\t\t\t\t\tresolve(valid); // 进入promise的then方法\n\t\t\t\t\t\t\t// 判断是否设置了toast的提示方式，只提示最前面的表单域的第一个错误信息\n\t\t\t\t\t\t\tif(this.errorType.indexOf('none') === -1 && this.errorType.indexOf('toast') >= 0 && errorArr.length) {\n\t\t\t\t\t\t\t\tthis.$u.toast(errorArr[0]);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 调用回调方法\n\t\t\t\t\t\t\tif (typeof callback == 'function') callback(valid);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../libs/css/style.components.scss\";\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-form.vue?vue&type=style&index=0&id=786a592e&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-form.vue?vue&type=style&index=0&id=786a592e&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699051728\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}