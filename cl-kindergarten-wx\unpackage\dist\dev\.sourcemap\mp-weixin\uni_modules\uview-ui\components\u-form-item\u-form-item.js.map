{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form-item/u-form-item.vue?9baa", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form-item/u-form-item.vue?ee4e", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form-item/u-form-item.vue?01c3", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form-item/u-form-item.vue?7be1", "uni-app:///uni_modules/uview-ui/components/u-form-item/u-form-item.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form-item/u-form-item.vue?1f2e", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-form-item/u-form-item.vue?f350"], "names": ["schema", "name", "mixins", "inject", "uForm", "default", "props", "label", "type", "prop", "borderBottom", "labelPosition", "labelWidth", "labelStyle", "labelAlign", "rightIcon", "leftIcon", "leftIconStyle", "rightIconStyle", "required", "data", "initialValue", "validateState", "validateMessage", "errorType", "fieldValue", "parentData", "watch", "computed", "uLabel<PERSON>idth", "<PERSON><PERSON><PERSON><PERSON>", "showError", "elLabelStyle", "elLabelPosition", "elLabelAlign", "elBorderBottom", "methods", "broadcastInputError", "setRules", "getRules", "rules", "onFieldBlur", "onFieldChange", "getFilteredRule", "validation", "validator", "firstFields", "callback", "reset<PERSON>ield", "mounted", "Object", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC4CznB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,gBAmBA;EACAC;EACAC;EACAC;IACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAH;IACA;IACA;IACAI;MACAD;MACAH;IACA;IACA;IACAK;MACAF;MACAH;IACA;IACA;IACAM;MACAH;MACAH;IACA;IACA;IACAO;MACAJ;MACAH;IACA;IACA;IACAQ;MACAL;MACAH;QACA;MACA;IACA;IACA;IACAS;MACAN;MACAH;IACA;IACA;IACAU;MACAP;MACAH;IACA;IACA;IACAW;MACAR;MACAH;IACA;IACA;IACAY;MACAT;MACAH;QACA;MACA;IACA;IACA;IACAa;MACAV;MACAH;QACA;MACA;IACA;IACA;IACAc;MACAX;MACAH;IACA;EACA;EACAe;IACA;MACAC;MAAA;MACA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MACAC;MAAA;MACA;MACAC;QACAhB;QACAE;QACAD;QACAE;QACAC;MACA;IACA;EACA;EACAa;IACAL;MACA;IACA;IACA;IACA;MACA;MACA;IACA;EACA;EACAM;IACA;IACAC;MACA;MACA,mHACAC;IACA;IACAC;MAAA;MACA;QACA;QACA,4DACA,yDACA;MACA;IACA;IACA;IACAD;MACA;MACA,sHACAlB,aACA;IACA;IACA;IACAoB;MACA,yHACA;IACA;IACA;IACAC;MACA,iHACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA,oHACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA,8EACA,kBACA;MACAC,qDACA,6BACA;QACAC;MACA;QACA;QACA;QACA;QACA;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;EACA;EAEA;EACAC;IAAA;IACA;IACA;IACA;MACA;MACAC;QACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;EAEA;EACAC;IAAA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACxVA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,8oCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-form-item/u-form-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-form-item.vue?vue&type=template&id=067e4733&scoped=true&\"\nvar renderjs\nimport script from \"./u-form-item.vue?vue&type=script&lang=js&\"\nexport * from \"./u-form-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-form-item.vue?vue&type=style&index=0&id=067e4733&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"067e4733\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-form-item.vue?vue&type=template&id=067e4733&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.validateState === \"error\" && _vm.showError(\"border-bottom\")\n  var s0 =\n    _vm.required || _vm.leftIcon || _vm.label\n      ? _vm.__get_style([\n          _vm.elLabelStyle,\n          {\n            \"justify-content\":\n              _vm.elLabelAlign == \"left\"\n                ? \"flex-start\"\n                : _vm.elLabelAlign == \"center\"\n                ? \"center\"\n                : \"flex-end\",\n          },\n        ])\n      : null\n  var m1 = _vm.validateState === \"error\" && _vm.showError(\"message\")\n  var g0 =\n    m1 && _vm.elLabelPosition == \"left\"\n      ? _vm.$u.addUnit(_vm.elLabelWidth)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        s0: s0,\n        m1: m1,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-form-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-form-item.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-form-item\" :class=\"{'u-border-bottom': elBorderBottom, 'u-form-item__border-bottom--error': validateState === 'error' && showError('border-bottom')}\">\n\t\t<view class=\"u-form-item__body\" :style=\"{\n\t\t\tflexDirection: elLabelPosition == 'left' ? 'row' : 'column'\n\t\t}\">\n\t\t\t<!-- 微信小程序中，将一个参数设置空字符串，结果会变成字符串\"true\" -->\n\t\t\t<view class=\"u-form-item--left\" :style=\"{\n\t\t\t\twidth: uLabelWidth,\n\t\t\t\tflex: `0 0 ${uLabelWidth}`,\n\t\t\t\tmarginBottom: elLabelPosition == 'left' ? 0 : '10rpx',\n\t\t\t}\">\n\t\t\t\t<!-- 为了块对齐 -->\n\t\t\t\t<view class=\"u-form-item--left__content\" v-if=\"required || leftIcon || label\">\n\t\t\t\t\t<!-- nvue不支持伪元素before -->\n\t\t\t\t\t<text v-if=\"required\" class=\"u-form-item--left__content--required\">*</text>\n\t\t\t\t\t<view class=\"u-form-item--left__content__icon\" v-if=\"leftIcon\">\n\t\t\t\t\t\t<u-icon :name=\"leftIcon\" :custom-style=\"leftIconStyle\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"u-form-item--left__content__label\" :style=\"[elLabelStyle, {\n\t\t\t\t\t\t'justify-content': elLabelAlign == 'left' ? 'flex-start' : elLabelAlign == 'center' ? 'center' : 'flex-end'\n\t\t\t\t\t}]\">\n\t\t\t\t\t\t{{label}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"u-form-item--right u-flex\">\n\t\t\t\t<view class=\"u-form-item--right__content\">\n\t\t\t\t\t<view class=\"u-form-item--right__content__slot \">\n\t\t\t\t\t\t<slot />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"u-form-item--right__content__icon u-flex\" v-if=\"$slots.right || rightIcon\">\n\t\t\t\t\t\t<u-icon :custom-style=\"rightIconStyle\" v-if=\"rightIcon\" :name=\"rightIcon\"></u-icon>\n\t\t\t\t\t\t<slot name=\"right\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"u-form-item__message\" v-if=\"validateState === 'error' && showError('message')\" :style=\"{\n\t\t\tpaddingLeft: elLabelPosition == 'left' ? $u.addUnit(elLabelWidth) : '0',\n\t\t}\">{{validateMessage}}</view>\n\t</view>\n</template>\n\n<script>\n\timport Emitter from '../../libs/util/emitter.js';\n\timport schema from '../../libs/util/async-validator';\n\t// 去除警告信息\n\tschema.warning = function() {};\n\n\t/**\n\t * form-item 表单item\n\t * @description 此组件一般用于表单场景，可以配置Input输入框，Select弹出框，进行表单验证等。\n\t * @tutorial http://uviewui.com/components/form.html\n\t * @property {String} label 左侧提示文字\n\t * @property {Object} prop 表单域model对象的属性名，在使用 validate、resetFields 方法的情况下，该属性是必填的\n\t * @property {Boolean} border-bottom 是否显示表单域的下划线边框\n\t * @property {String} label-position 表单域提示文字的位置，left-左侧，top-上方\n\t * @property {String Number} label-width 提示文字的宽度，单位rpx（默认90）\n\t * @property {Object} label-style lable的样式，对象形式\n\t * @property {String} label-align lable的对齐方式\n\t * @property {String} right-icon 右侧自定义字体图标(限uView内置图标)或图片地址\n\t * @property {String} left-icon 左侧自定义字体图标(限uView内置图标)或图片地址\n\t * @property {Object} left-icon-style 左侧图标的样式，对象形式\n\t * @property {Object} right-icon-style 右侧图标的样式，对象形式\n\t * @property {Boolean} required 是否显示左边的\"*\"号，这里仅起展示作用，如需校验必填，请通过rules配置必填规则(默认false)\n\t * @example <u-form-item label=\"姓名\"><u-input v-model=\"form.name\" /></u-form-item>\n\t */\n\n\texport default {\n\t\tname: 'u-form-item',\n\t\tmixins: [Emitter],\n\t\tinject: {\n\t\t\tuForm: {\n\t\t\t\tdefault () {\n\t\t\t\t\treturn null\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tprops: {\n\t\t\t// input的label提示语\n\t\t\tlabel: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 绑定的值\n\t\t\tprop: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 是否显示表单域的下划线边框\n\t\t\tborderBottom: {\n\t\t\t\ttype: [String, Boolean],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// label的位置，left-左边，top-上边\n\t\t\tlabelPosition: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// label的宽度，单位rpx\n\t\t\tlabelWidth: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// lable的样式，对象形式\n\t\t\tlabelStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn {}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// lable字体的对齐方式\n\t\t\tlabelAlign: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 右侧图标\n\t\t\trightIcon: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 左侧图标\n\t\t\tleftIcon: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 左侧图标的样式\n\t\t\tleftIconStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn {}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 左侧图标的样式\n\t\t\trightIconStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn {}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 是否显示左边的必填星号，只作显示用，具体校验必填的逻辑，请在rules中配置\n\t\t\trequired: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tinitialValue: '', // 存储的默认值\n\t\t\t\t// isRequired: false, // 是否必填，由于人性化考虑，必填\"*\"号通过props的required配置，不再通过rules的规则自动生成\n\t\t\t\tvalidateState: '', // 是否校验成功\n\t\t\t\tvalidateMessage: '', // 校验失败的提示语\n\t\t\t\t// 有错误时的提示方式，message-提示信息，border-如果input设置了边框，变成呈红色，\n\t\t\t\terrorType: ['message'],\n\t\t\t\tfieldValue: '', // 获取当前子组件input的输入的值\n\t\t\t\t// 父组件的参数，在computed计算中，无法得知this.parent发生变化，故将父组件的参数值，放到data中\n\t\t\t\tparentData: {\n\t\t\t\t\tborderBottom: true,\n\t\t\t\t\tlabelWidth: 90,\n\t\t\t\t\tlabelPosition: 'left',\n\t\t\t\t\tlabelStyle: {},\n\t\t\t\t\tlabelAlign: 'left',\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\twatch: {\n\t\t\tvalidateState(val) {\n\t\t\t\tthis.broadcastInputError();\n\t\t\t},\n\t\t\t// 监听u-form组件的errorType的变化\n\t\t\t\"uForm.errorType\"(val) {\n\t\t\t\tthis.errorType = val;\n\t\t\t\tthis.broadcastInputError();\n\t\t\t},\n\t\t},\n\t\tcomputed: {\n\t\t\t// 计算后的label宽度，由于需要多个判断，故放到computed中\n\t\t\tuLabelWidth() {\n\t\t\t\t// 如果用户设置label为空字符串(微信小程序空字符串最终会变成字符串的'true')，意味着要将label的位置宽度设置为auto\n\t\t\t\treturn this.elLabelPosition == 'left' ? (this.label === 'true' || this.label === '' ? 'auto' : this.$u.addUnit(this\n\t\t\t\t\t.elLabelWidth)) : '100%';\n\t\t\t},\n\t\t\tshowError() {\n\t\t\t\treturn type => {\n\t\t\t\t\t// 如果errorType数组中含有none，或者toast提示类型\n\t\t\t\t\tif (this.errorType.indexOf('none') >= 0) return false;\n\t\t\t\t\telse if (this.errorType.indexOf(type) >= 0) return true;\n\t\t\t\t\telse return false;\n\t\t\t\t}\n\t\t\t},\n\t\t\t// label的宽度\n\t\t\telLabelWidth() {\n\t\t\t\t// label默认宽度为90，优先使用本组件的值，如果没有(如果设置为0，也算是配置了值，依然起效)，则用u-form的值\n\t\t\t\treturn (this.labelWidth != 0 || this.labelWidth != '') ? this.labelWidth : (this.parentData.labelWidth ? this.parentData\n\t\t\t\t\t.labelWidth :\n\t\t\t\t\t90);\n\t\t\t},\n\t\t\t// label的样式\n\t\t\telLabelStyle() {\n\t\t\t\treturn Object.keys(this.labelStyle).length ? this.labelStyle : (this.parentData.labelStyle ? this.parentData.labelStyle :\n\t\t\t\t\t{});\n\t\t\t},\n\t\t\t// label的位置，左侧或者上方\n\t\t\telLabelPosition() {\n\t\t\t\treturn this.labelPosition ? this.labelPosition : (this.parentData.labelPosition ? this.parentData.labelPosition :\n\t\t\t\t\t'left');\n\t\t\t},\n\t\t\t// label的对齐方式\n\t\t\telLabelAlign() {\n\t\t\t\treturn this.labelAlign ? this.labelAlign : (this.parentData.labelAlign ? this.parentData.labelAlign : 'left');\n\t\t\t},\n\t\t\t// label的下划线\n\t\t\telBorderBottom() {\n\t\t\t\t// 子组件的borderBottom默认为空字符串，如果不等于空字符串，意味着子组件设置了值，优先使用子组件的值\n\t\t\t\treturn this.borderBottom !== '' ? this.borderBottom : this.parentData.borderBottom ? this.parentData.borderBottom :\n\t\t\t\t\ttrue;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tbroadcastInputError() {\n\t\t\t\t// 子组件发出事件，第三个参数为true或者false，true代表有错误\n\t\t\t\tthis.broadcast('u-input', 'on-form-item-error', this.validateState === 'error' && this.showError('border'));\n\t\t\t},\n\t\t\t// 判断是否需要required校验\n\t\t\tsetRules() {\n\t\t\t\tlet that = this;\n\t\t\t\t// 由于人性化考虑，必填\"*\"号通过props的required配置，不再通过rules的规则自动生成\n\t\t\t\t// 从父组件u-form拿到当前u-form-item需要验证 的规则\n\t\t\t\t// let rules = this.getRules();\n\t\t\t\t// if (rules.length) {\n\t\t\t\t// \tthis.isRequired = rules.some(rule => {\n\t\t\t\t// \t\t// 如果有必填项，就返回，没有的话，就是undefined\n\t\t\t\t// \t\treturn rule.required;\n\t\t\t\t// \t});\n\t\t\t\t// }\n\n\t\t\t\t// blur事件\n\t\t\t\tthis.$on('on-form-blur', that.onFieldBlur);\n\t\t\t\t// change事件\n\t\t\t\tthis.$on('on-form-change', that.onFieldChange);\n\t\t\t},\n\n\t\t\t// 从u-form的rules属性中，取出当前u-form-item的校验规则\n\t\t\tgetRules() {\n\t\t\t\t// 父组件的所有规则\n\t\t\t\tlet rules = this.parent.rules;\n\t\t\t\trules = rules ? rules[this.prop] : [];\n\t\t\t\t// 保证返回的是一个数组形式\n\t\t\t\treturn [].concat(rules || []);\n\t\t\t},\n\n\t\t\t// blur事件时进行表单校验\n\t\t\tonFieldBlur() {\n\t\t\t\tthis.validation('blur');\n\t\t\t},\n\n\t\t\t// change事件进行表单校验\n\t\t\tonFieldChange() {\n\t\t\t\tthis.validation('change');\n\t\t\t},\n\n\t\t\t// 过滤出符合要求的rule规则\n\t\t\tgetFilteredRule(triggerType = '') {\n\t\t\t\tlet rules = this.getRules();\n\t\t\t\t// 整体验证表单时，triggerType为空字符串，此时返回所有规则进行验证\n\t\t\t\tif (!triggerType) return rules;\n\t\t\t\t// 历遍判断规则是否有对应的事件，比如blur，change触发等的事件\n\t\t\t\t// 使用indexOf判断，是因为某些时候设置的验证规则的trigger属性可能为多个，比如['blur','change']\n\t\t\t\t// 某些场景可能的判断规则，可能不存在trigger属性，故先判断是否存在此属性\n\t\t\t\treturn rules.filter(res => res.trigger && res.trigger.indexOf(triggerType) !== -1);\n\t\t\t},\n\n\t\t\t// 校验数据\n\t\t\tvalidation(trigger, callback = () => {}) {\n\t\t\t\t// 检验之间，先获取需要校验的值\n\t\t\t\tthis.fieldValue = this.parent.model[this.prop];\n\t\t\t\t// blur和change是否有当前方式的校验规则\n\t\t\t\tlet rules = this.getFilteredRule(trigger);\n\t\t\t\t// 判断是否有验证规则，如果没有规则，也调用回调方法，否则父组件u-form会因为\n\t\t\t\t// 对count变量的统计错误而无法进入上一层的回调\n\t\t\t\tif (!rules || rules.length === 0) {\n\t\t\t\t\treturn callback('');\n\t\t\t\t}\n\t\t\t\t// 设置当前的装填，标识为校验中\n\t\t\t\tthis.validateState = 'validating';\n\t\t\t\t// 调用async-validator的方法\n\t\t\t\tlet validator = new schema({\n\t\t\t\t\t[this.prop]: rules\n\t\t\t\t});\n\t\t\t\tvalidator.validate({\n\t\t\t\t\t[this.prop]: this.fieldValue\n\t\t\t\t}, {\n\t\t\t\t\tfirstFields: true\n\t\t\t\t}, (errors, fields) => {\n\t\t\t\t\t// 记录状态和报错信息\n\t\t\t\t\tthis.validateState = !errors ? 'success' : 'error';\n\t\t\t\t\tthis.validateMessage = errors ? errors[0].message : '';\n\t\t\t\t\t// 调用回调方法\n\t\t\t\t\tcallback(this.validateMessage);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 清空当前的u-form-item\n\t\t\tresetField() {\n\t\t\t\tthis.parent.model[this.prop] = this.initialValue;\n\t\t\t\t// 设置为`success`状态，只是为了清空错误标记\n\t\t\t\tthis.validateState = 'success';\n\t\t\t}\n\t\t},\n\n\t\t// 组件创建完成时，将当前实例保存到u-form中\n\t\tmounted() {\n\t\t\t// 支付宝、头条小程序不支持provide/inject，所以使用这个方法获取整个父组件，在created定义，避免循环应用\n\t\t\tthis.parent = this.$u.$parent.call(this, 'u-form');\n\t\t\tif (this.parent) {\n\t\t\t\t// 历遍parentData中的属性，将parent中的同名属性赋值给parentData\n\t\t\t\tObject.keys(this.parentData).map(key => {\n\t\t\t\t\tthis.parentData[key] = this.parent[key];\n\t\t\t\t});\n\t\t\t\t// 如果没有传入prop，或者uForm为空(如果u-form-input单独使用，就不会有uForm注入)，就不进行校验\n\t\t\t\tif (this.prop) {\n\t\t\t\t\t// 将本实例添加到父组件中\n\t\t\t\t\tthis.parent.fields.push(this);\n\t\t\t\t\tthis.errorType = this.parent.errorType;\n\t\t\t\t\t// 设置初始值\n\t\t\t\t\tthis.initialValue = this.fieldValue;\n\t\t\t\t\t// 添加表单校验，这里必须要写在$nextTick中，因为u-form的rules是通过ref手动传入的\n\t\t\t\t\t// 不在$nextTick中的话，可能会造成执行此处代码时，父组件还没通过ref把规则给u-form，导致规则为空\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.setRules();\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t// 组件销毁前，将实例从u-form的缓存中移除\n\t\tbeforeDestroy() {\n\t\t\t// 如果当前没有prop的话表示当前不要进行删除（因为没有注入）\n\t\t\tif (this.parent && this.prop) {\n\t\t\t\tthis.parent.fields.map((item, index) => {\n\t\t\t\t\tif (item === this) this.parent.fields.splice(index, 1);\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/style.components.scss\";\n\n\t.u-form-item {\n\t\t@include vue-flex;\n\t\t// align-items: flex-start;\n\t\tpadding: 20rpx 0;\n\t\tfont-size: 28rpx;\n\t\tcolor: $u-main-color;\n\t\tbox-sizing: border-box;\n\t\tline-height: $u-form-item-height;\n\t\tflex-direction: column;\n\n\t\t&__border-bottom--error:after {\n\t\t\tborder-color: $u-type-error;\n\t\t}\n\n\t\t&__body {\n\t\t\t@include vue-flex;\n\t\t}\n\n\t\t&--left {\n\t\t\t@include vue-flex;\n\t\t\talign-items: center;\n\n\t\t\t&__content {\n\t\t\t\tposition: relative;\n\t\t\t\t@include vue-flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding-right: 10rpx;\n\t\t\t\tflex: 1;\n\n\t\t\t\t&__icon {\n\t\t\t\t\tmargin-right: 8rpx;\n\t\t\t\t}\n\n\t\t\t\t&--required {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tleft: -16rpx;\n\t\t\t\t\tvertical-align: middle;\n\t\t\t\t\tcolor: $u-type-error;\n\t\t\t\t\tpadding-top: 6rpx;\n\t\t\t\t}\n\n\t\t\t\t&__label {\n\t\t\t\t\t@include vue-flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tflex: 1;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&--right {\n\t\t\tflex: 1;\n\n\t\t\t&__content {\n\t\t\t\t@include vue-flex;\n\t\t\t\talign-items: center;\n\t\t\t\tflex: 1;\n\n\t\t\t\t&__slot {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\t/* #ifndef MP */\n\t\t\t\t\t@include vue-flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\t/* #endif */\n\t\t\t\t}\n\n\t\t\t\t&__icon {\n\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t\tcolor: $u-light-color;\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&__message {\n\t\t\tfont-size: 24rpx;\n\t\t\tline-height: 24rpx;\n\t\t\tcolor: $u-type-error;\n\t\t\tmargin-top: 12rpx;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-form-item.vue?vue&type=style&index=0&id=067e4733&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-form-item.vue?vue&type=style&index=0&id=067e4733&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699051850\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}