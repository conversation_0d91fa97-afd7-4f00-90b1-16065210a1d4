{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/App.vue?8506", "uni-app:///App.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/App.vue?3f9b", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/App.vue?0a1b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "uView", "prototype", "$api", "api", "$API_BASE_URL", "BASE_URL", "$systemConfig", "config", "vuexStore", "require", "mixin", "productionTip", "App", "mpType", "app", "store", "$mount", "onLaunch", "onShow", "updateManager", "uni", "title", "content", "success", "icon", "onHide"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AAEA;AACA;AAIA;AAGA;AAIA;AAGA;AAAwC;AAAA;AAAA;AAAA;AAnBxC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAK1DC,YAAG,CAACC,GAAG,CAACC,gBAAK,CAAC;AAOdF,YAAG,CAACG,SAAS,CAACC,IAAI,GAAGC,GAAG;AAGY;AACpCL,YAAG,CAACG,SAAS,CAACG,aAAa,GAAEC,YAAQ;AAGrCP,YAAG,CAACG,SAAS,CAACK,aAAa,GAAGC,eAAM;AAEpC,IAAIC,SAAS,GAAGC,mBAAO,CAAC,6BAAqB,CAAC;AAC9CX,YAAG,CAACY,KAAK,CAACF,SAAS,CAAC;AAEpBV,YAAG,CAACS,MAAM,CAACI,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIhB,YAAG;EAClBiB,KAAK,EAALA;AAAK,GACDH,YAAG,EACN;AACF,UAAAE,GAAG,EAACE,MAAM,EAAE,C;;;;;;;;;;;;;AChCZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAA6lB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCMjnB;EACAC;IACA;EAAA,CAiBA;EACAC;IAGA;IACAC;MACA;QAAA;QACAC;UAAAC;QAAA;MACA;IACA;IACA;IACAF;MACAC;MACAA;QAAA;QACAC;QACAC;QACAC;UACA;YACAJ;UACA;QACA;MACA;IACA;IACA;IACAA;MACAC;MACAA;QAAAC;QAAAG;MAAA;IACA;EAEA;EACAC,2BAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAwoC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACA5pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App'\r\nimport '@/utils/interceptor.js';//引入拦截\r\n\r\nimport Vue from 'vue'\r\nimport uView from '@/uni_modules/uview-ui'\r\nVue.use(uView)\r\n\r\n\r\nimport store from './store'\r\n\r\n// 注册请求方法\r\nimport * as api from '@/api/api.js'\r\nVue.prototype.$api = api\r\n\r\n\r\nimport BASE_URL from '@/api/env.js' //引入接口共用地址\r\nVue.prototype.$API_BASE_URL= BASE_URL\r\n\r\nimport config from '@/config/config.js';\r\nVue.prototype.$systemConfig = config\r\n\r\nlet vuexStore = require(\"@/store/$u.mixin.js\");\r\nVue.mixin(vuexStore);\r\n\r\nVue.config.productionTip = false   \r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n\tstore,\r\n  ...App\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\r\n\t// #ifdef APP-PLUS\r\n\timport appUpdate from '@/components/appUpdate/appUpdate.js'\r\n\t// #endif\r\n\t\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t  //console.log('App Launch')\r\n\t\t   // #ifdef APP-PLUS\r\n\t\t  let systemInfo = uni.getSystemInfoSync()\r\n\t\t  const data={\r\n\t\t  \tappid: systemInfo.appId,\r\n\t\t  \tversion: systemInfo.appVersion\r\n\t\t  }\r\n\t\t  console.log(data)\r\n\t\t  //把appid和版本发送请求服务器，看服务器上是否有更新\t\r\n\t\t  this.$api.checkVersion(data).then(res => {\r\n\t\t\t  console.log(res)\r\n\t\t  \tif(res.code==0){\r\n\t\t\t\t//如果返回0则表示有更新\r\n\t\t  \t\tappUpdate(res)  \r\n\t\t  \t}\r\n\t\t  })    \r\n\t\t  // #endif\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\r\n\t\t\t// 当向小程序后台请求完新版本信息，会进行回调。res: {hasUpdate: true, version: 1.0.0}\r\n\t\t\tupdateManager.onCheckForUpdate(function (res) {\r\n\t\t\t\tif (res.hasUpdate) {\t\t\t\t\t // 有更新\r\n\t\t\t\t\tuni.showLoading({title:'更新中...'}); // 开始下载前，显示Loading\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// 当新版本下载完成，会进行回调\r\n\t\t\tupdateManager.onUpdateReady(function () {\r\n\t\t\t\tuni.hideLoading();   // 关闭 Loading \r\n\t\t\t\tuni.showModal({\t\t// 弹确认框（强制更新）\r\n\t\t\t\t\ttitle:'更新提示',\r\n\t\t\t\t\tcontent:'更新完毕，是否重启？',\r\n\t\t\t\t\tsuccess:function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tupdateManager.applyUpdate(); // 强制小程序重启并使用新版本。\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t});\r\n\t\t\t// 当新版本下载失败，会进行回调\r\n\t\t\tupdateManager.onUpdateFailed(function () {\r\n\t\t\t\tuni.hideLoading();\t// 关闭 Loading \r\n\t\t\t\tuni.showToast({ title:'更新失败，稍后再试...', icon:\"error\" });\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonHide: function() { \r\n\t\t\t\r\n\t\t}\r\n\t} \r\n</script>\r\n<style lang=\"scss\">\r\n\t@import \"@/uni_modules/uview-ui/index.scss\";\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699051708\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}