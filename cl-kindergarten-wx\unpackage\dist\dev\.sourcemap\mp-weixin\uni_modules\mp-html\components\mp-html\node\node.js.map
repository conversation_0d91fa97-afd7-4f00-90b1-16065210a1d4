{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/node/node.vue?0bf7", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/node/node.vue?1655", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/node/node.vue?77c7", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/node/node.vue?4945", "uni-app:///uni_modules/mp-html/components/mp-html/node/node.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/node/node.vue?f2ca", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/node/node.vue?3fd3", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/node/node.vue?c6b6", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/mp-html/components/mp-html/node/node.vue?90e3"], "names": ["name", "options", "virtualHost", "data", "ctrl", "isiOS", "props", "attrs", "type", "default", "childs", "opts", "components", "node", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "toJSON", "play", "flag", "ctx", "imgTap", "uni", "showmenu", "current", "urls", "imgLongTap", "imgLoad", "checkReady", "setTimeout", "linkTap", "innerText", "success", "title", "url", "fail", "mediaError", "index", "source", "errMsg"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA63B;AAC73B;AACwD;AACL;AACa;;;AAGhE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,21BAAM;AACR,EAAE,o2BAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+1BAAU;AACZ;AACA;;AAEA;AACgG;AAChG,WAAW,kHAAM,iBAAiB,0HAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;gBC8GlnB;EACAA;EACAC;IAEAC;EAKA;EACAC;IACA;MACAC;MAEAC;IAEA;EACA;EACAC;IACAN;IACAO;MACAC;MACAC;QACA;MACA;IACA;IACAC;IACAC;EACA;EACAC;IAGAC;EAEA;EACAC;IAAA;IACA;MACA;QAAA;MAAA;IACA;EAqBA;EACAC,yCAMA;EACAC;IAEAC;MAAA;IAAA;IAEA;AACA;AACA;AACA;IACAC;MACA;MAEA;QACA;QACA;QACA;UACA;YACAC;UACA;YACA;UACA;QACA;QACA;QACA;UACA,qCAEA,KAEA;UACAC;UACA;YACAA;UACA;UACA;QACA;MACA;IAEA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;MACA;MACA;MAIA;MACA;MACA;QACAC;UAEAC;UAMAC;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC,oCA6BA;IAEA;AACA;AACA;AACA;IACAC;MACA;MAEA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACA;QACA;QACA;UACAC;YACA;cACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACA;UACA;UACA;QACA;UACA;UACA;YAKAT;cACAnB;cACA6B;gBAAA,OACAV;kBACAW;gBACA;cAAA;YACA;UAKA;QACA;UACA;UACAX;YACAY;YACAC;cACAb;gBACAY;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACA;UACAC;QACA;QACA;UACA;UACA;QACA;MACA;QAIA;QACA;UACA;QACA;QACA;MACA;MACA;QACA;UACAC;UACA/B;UAEAgC;QAEA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AClZA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,i3BAAG,EAAC,C;;;;;;;;;;;ACAp4B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAA0c,CAAgB,yfAAG,EAAC,C;;;;;;;;;;;;ACA9d;AAAe;AACf;AACA;AACA;;AAEA,M", "file": "uni_modules/mp-html/components/mp-html/node/node.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./node.vue?vue&type=template&id=35a45afb&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiLy8g6KGM5YaF5qCH562%2B5YiX6KGoXG52YXIgaW5saW5lVGFncyA9IHtcbiAgYWJicjogdHJ1ZSxcbiAgYjogdHJ1ZSxcbiAgYmlnOiB0cnVlLFxuICBjb2RlOiB0cnVlLFxuICBkZWw6IHRydWUsXG4gIGVtOiB0cnVlLFxuICBpOiB0cnVlLFxuICBpbnM6IHRydWUsXG4gIGxhYmVsOiB0cnVlLFxuICBxOiB0cnVlLFxuICBzbWFsbDogdHJ1ZSxcbiAgc3BhbjogdHJ1ZSxcbiAgc3Ryb25nOiB0cnVlLFxuICBzdWI6IHRydWUsXG4gIHN1cDogdHJ1ZVxufVxuLyoqXG4gKiBAZGVzY3JpcHRpb24g5Yik5pat5piv5ZCm5Li66KGM5YaF5qCH562%2BXG4gKi9cbm1vZHVsZS5leHBvcnRzID0ge1xuICBpc0lubGluZTogZnVuY3Rpb24gKHRhZ05hbWUsIHN0eWxlKSB7XG4gICAgcmV0dXJuIGlubGluZVRhZ3NbdGFnTmFtZV0gfHwgKHN0eWxlIHx8ICcnKS5pbmRleE9mKCdkaXNwbGF5OmlubGluZScpICE9PSAtMVxuICB9XG59Iiwic3RhcnQiOjYwOTYsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIn0sImVuZCI6NjUwNX19&\"\nvar renderjs\nimport script from \"./node.vue?vue&type=script&lang=js&\"\nexport * from \"./node.vue?vue&type=script&lang=js&\"\nimport style0 from \"./node.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./node.vue?vue&type=custom&index=0&blockType=script&module=handler&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"uni_modules/mp-html/components/mp-html/node/node.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./node.vue?vue&type=template&id=35a45afb&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiLy8g6KGM5YaF5qCH562%2B5YiX6KGoXG52YXIgaW5saW5lVGFncyA9IHtcbiAgYWJicjogdHJ1ZSxcbiAgYjogdHJ1ZSxcbiAgYmlnOiB0cnVlLFxuICBjb2RlOiB0cnVlLFxuICBkZWw6IHRydWUsXG4gIGVtOiB0cnVlLFxuICBpOiB0cnVlLFxuICBpbnM6IHRydWUsXG4gIGxhYmVsOiB0cnVlLFxuICBxOiB0cnVlLFxuICBzbWFsbDogdHJ1ZSxcbiAgc3BhbjogdHJ1ZSxcbiAgc3Ryb25nOiB0cnVlLFxuICBzdWI6IHRydWUsXG4gIHN1cDogdHJ1ZVxufVxuLyoqXG4gKiBAZGVzY3JpcHRpb24g5Yik5pat5piv5ZCm5Li66KGM5YaF5qCH562%2BXG4gKi9cbm1vZHVsZS5leHBvcnRzID0ge1xuICBpc0lubGluZTogZnVuY3Rpb24gKHRhZ05hbWUsIHN0eWxlKSB7XG4gICAgcmV0dXJuIGlubGluZVRhZ3NbdGFnTmFtZV0gfHwgKHN0eWxlIHx8ICcnKS5pbmRleE9mKCdkaXNwbGF5OmlubGluZScpICE9PSAtMVxuICB9XG59Iiwic3RhcnQiOjYwOTYsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIn0sImVuZCI6NjUwNX19&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./node.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./node.vue?vue&type=script&lang=js&\"", "<template>\n  <view :id=\"attrs.id\" :class=\"'_block _'+name+' '+attrs.class\" :style=\"attrs.style\">\n    <block v-for=\"(n, i) in childs\" v-bind:key=\"i\">\n      <!-- 图片 -->\n      <!-- 占位图 -->\n      <image v-if=\"n.name==='img'&&!n.t&&((opts[1]&&!ctrl[i])||ctrl[i]<0)\" class=\"_img\" :style=\"n.attrs.style\" :src=\"ctrl[i]<0?opts[2]:opts[1]\" mode=\"widthFix\" />\n      <!-- 显示图片 -->\n      <!-- #ifdef H5 || (APP-PLUS && VUE2) -->\n      <img v-if=\"n.name==='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]===-1?'display:none;':'')+n.attrs.style\" :src=\"n.attrs.src||(ctrl.load?n.attrs['data-src']:'')\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\" />\n      <!-- #endif -->\n      <!-- #ifndef H5 || (APP-PLUS && VUE2) -->\n      <!-- 表格中的图片，使用 rich-text 防止大小不正确 -->\n      <rich-text v-if=\"n.name==='img'&&n.t\" :style=\"'display:'+n.t\" :nodes=\"[{attrs:{style:n.attrs.style,src:n.attrs.src},name:'img'}]\" :data-i=\"i\" @tap.stop=\"imgTap\" />\n      <!-- #endif -->\n      <!-- #ifndef H5 || APP-PLUS -->\n      <image v-else-if=\"n.name==='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]===-1?'display:none;':'')+'width:'+(ctrl[i]||1)+'px;height:1px;'+n.attrs.style\" :src=\"n.attrs.src\" :mode=\"!n.h?'widthFix':(!n.w?'heightFix':'')\" :lazy-load=\"opts[0]\" :webp=\"n.webp\" :show-menu-by-longpress=\"opts[3]&&!n.attrs.ignore\" :image-menu-prevent=\"!opts[3]||n.attrs.ignore\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\" />\n      <!-- #endif -->\n      <!-- #ifdef APP-PLUS && VUE3 -->\n      <image v-else-if=\"n.name==='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]===-1?'display:none;':'')+'width:'+(ctrl[i]||1)+'px;'+n.attrs.style\" :src=\"n.attrs.src||(ctrl.load?n.attrs['data-src']:'')\" :mode=\"!n.h?'widthFix':(!n.w?'heightFix':'')\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\" />\n      <!-- #endif -->\n      <!-- 文本 -->\n      <!-- #ifdef MP-WEIXIN -->\n      <text v-else-if=\"n.text\" :user-select=\"opts[4]=='force'&&isiOS\" decode>{{n.text}}</text>\n      <!-- #endif -->\n      <!-- #ifndef MP-WEIXIN || MP-BAIDU || MP-ALIPAY || MP-TOUTIAO -->\n      <text v-else-if=\"n.text\" decode>{{n.text}}</text>\n      <!-- #endif -->\n      <text v-else-if=\"n.name==='br'\">\\n</text>\n      <!-- 链接 -->\n      <view v-else-if=\"n.name==='a'\" :id=\"n.attrs.id\" :class=\"(n.attrs.href?'_a ':'')+n.attrs.class\" hover-class=\"_hover\" :style=\"'display:inline;'+n.attrs.style\" :data-i=\"i\" @tap.stop=\"linkTap\">\n        <node name=\"span\" :childs=\"n.children\" :opts=\"opts\" style=\"display:inherit\" />\n      </view>\n      <!-- 视频 -->\n      <!-- #ifdef APP-PLUS -->\n      <view v-else-if=\"n.html\" :id=\"n.attrs.id\" :class=\"'_video '+n.attrs.class\" :style=\"n.attrs.style\" v-html=\"n.html\" @vplay.stop=\"play\" />\n      <!-- #endif -->\n      <!-- #ifndef APP-PLUS -->\n      <video v-else-if=\"n.name==='video'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :autoplay=\"n.attrs.autoplay\" :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :muted=\"n.attrs.muted\" :object-fit=\"n.attrs['object-fit']\" :poster=\"n.attrs.poster\" :src=\"n.src[ctrl[i]||0]\" :data-i=\"i\" @play=\"play\" @error=\"mediaError\" />\n      <!-- #endif -->\n      <!-- #ifdef H5 || APP-PLUS -->\n      <iframe v-else-if=\"n.name==='iframe'\" :style=\"n.attrs.style\" :allowfullscreen=\"n.attrs.allowfullscreen\" :frameborder=\"n.attrs.frameborder\" :src=\"n.attrs.src\" />\n      <embed v-else-if=\"n.name==='embed'\" :style=\"n.attrs.style\" :src=\"n.attrs.src\" />\n      <!-- #endif -->\n      <!-- #ifndef MP-TOUTIAO || ((H5 || APP-PLUS) && VUE3) -->\n      <!-- 音频 -->\n      <audio v-else-if=\"n.name==='audio'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :author=\"n.attrs.author\" :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :name=\"n.attrs.name\" :poster=\"n.attrs.poster\" :src=\"n.src[ctrl[i]||0]\" :data-i=\"i\" @play=\"play\" @error=\"mediaError\" />\n      <!-- #endif -->\n      <view v-else-if=\"(n.name==='table'&&n.c)||n.name==='li'\" :id=\"n.attrs.id\" :class=\"'_'+n.name+' '+n.attrs.class\" :style=\"n.attrs.style\">\n        <node v-if=\"n.name==='li'\" :childs=\"n.children\" :opts=\"opts\" />\n        <view v-else v-for=\"(tbody, x) in n.children\" v-bind:key=\"x\" :class=\"'_'+tbody.name+' '+tbody.attrs.class\" :style=\"tbody.attrs.style\">\n          <node v-if=\"tbody.name==='td'||tbody.name==='th'\" :childs=\"tbody.children\" :opts=\"opts\" />\n          <block v-else v-for=\"(tr, y) in tbody.children\" v-bind:key=\"y\">\n            <view v-if=\"tr.name==='td'||tr.name==='th'\" :class=\"'_'+tr.name+' '+tr.attrs.class\" :style=\"tr.attrs.style\">\n              <node :childs=\"tr.children\" :opts=\"opts\" />\n            </view>\n            <view v-else :class=\"'_'+tr.name+' '+tr.attrs.class\" :style=\"tr.attrs.style\">\n              <view v-for=\"(td, z) in tr.children\" v-bind:key=\"z\" :class=\"'_'+td.name+' '+td.attrs.class\" :style=\"td.attrs.style\">\n                <node :childs=\"td.children\" :opts=\"opts\" />\n              </view>\n            </view>\n          </block>\n        </view>\n      </view>\n      \n      <!-- 富文本 -->\n      <!-- #ifdef H5 || ((MP-WEIXIN || MP-QQ || APP-PLUS || MP-360) && VUE2) -->\n      <rich-text v-else-if=\"!n.c&&!handler.isInline(n.name, n.attrs.style)\" :id=\"n.attrs.id\" :style=\"n.f\" :user-select=\"opts[4]\" :nodes=\"[n]\" />\n      <!-- #endif -->\n      <!-- #ifndef H5 || ((MP-WEIXIN || MP-QQ || APP-PLUS || MP-360) && VUE2) -->\n      <rich-text v-else-if=\"!n.c\" :id=\"n.attrs.id\" :style=\"'display:inline;'+n.f\" :preview=\"false\" :selectable=\"opts[4]\" :user-select=\"opts[4]\" :nodes=\"[n]\" />\n      <!-- #endif -->\n      <!-- 继续递归 -->\n      <view v-else-if=\"n.c===2\" :id=\"n.attrs.id\" :class=\"'_block _'+n.name+' '+n.attrs.class\" :style=\"n.f+';'+n.attrs.style\">\n        <node v-for=\"(n2, j) in n.children\" v-bind:key=\"j\" :style=\"n2.f\" :name=\"n2.name\" :attrs=\"n2.attrs\" :childs=\"n2.children\" :opts=\"opts\" />\n      </view>\n      <node v-else :style=\"n.f\" :name=\"n.name\" :attrs=\"n.attrs\" :childs=\"n.children\" :opts=\"opts\" />\n    </block>\n  </view>\n</template>\n<script module=\"handler\" lang=\"wxs\">\n// 行内标签列表\nvar inlineTags = {\n  abbr: true,\n  b: true,\n  big: true,\n  code: true,\n  del: true,\n  em: true,\n  i: true,\n  ins: true,\n  label: true,\n  q: true,\n  small: true,\n  span: true,\n  strong: true,\n  sub: true,\n  sup: true\n}\n/**\n * @description 判断是否为行内标签\n */\nmodule.exports = {\n  isInline: function (tagName, style) {\n    return inlineTags[tagName] || (style || '').indexOf('display:inline') !== -1\n  }\n}\n</script>\n<script>\n\nimport node from './node'\nexport default {\n  name: 'node',\n  options: {\n    // #ifdef MP-WEIXIN\n    virtualHost: true,\n    // #endif\n    // #ifdef MP-TOUTIAO\n    addGlobalClass: false\n    // #endif\n  },\n  data () {\n    return {\n      ctrl: {},\n      // #ifdef MP-WEIXIN\n      isiOS: uni.getSystemInfoSync().system.includes('iOS')\n      // #endif\n    }\n  },\n  props: {\n    name: String,\n    attrs: {\n      type: Object,\n      default () {\n        return {}\n      }\n    },\n    childs: Array,\n    opts: Array\n  },\n  components: {\n\n    // #ifndef (H5 || APP-PLUS) && VUE3\n    node\n    // #endif\n  },\n  mounted () {\n    this.$nextTick(() => {\n      for (this.root = this.$parent; this.root.$options.name !== 'mp-html'; this.root = this.root.$parent);\n    })\n    // #ifdef H5 || APP-PLUS\n    if (this.opts[0]) {\n      let i\n      for (i = this.childs.length; i--;) {\n        if (this.childs[i].name === 'img') break\n      }\n      if (i !== -1) {\n        this.observer = uni.createIntersectionObserver(this).relativeToViewport({\n          top: 500,\n          bottom: 500\n        })\n        this.observer.observe('._img', res => {\n          if (res.intersectionRatio) {\n            this.$set(this.ctrl, 'load', 1)\n            this.observer.disconnect()\n          }\n        })\n      }\n    }\n    // #endif\n  },\n  beforeDestroy () {\n    // #ifdef H5 || APP-PLUS\n    if (this.observer) {\n      this.observer.disconnect()\n    }\n    // #endif\n  },\n  methods:{\n    // #ifdef MP-WEIXIN\n    toJSON () { return this },\n    // #endif\n    /**\n     * @description 播放视频事件\n     * @param {Event} e\n     */\n    play (e) {\n      this.root.$emit('play')\n      // #ifndef APP-PLUS\n      if (this.root.pauseVideo) {\n        let flag = false\n        const id = e.target.id\n        for (let i = this.root._videos.length; i--;) {\n          if (this.root._videos[i].id === id) {\n            flag = true\n          } else {\n            this.root._videos[i].pause() // 自动暂停其他视频\n          }\n        }\n        // 将自己加入列表\n        if (!flag) {\n          const ctx = uni.createVideoContext(id\n            // #ifndef MP-BAIDU\n            , this\n            // #endif\n          )\n          ctx.id = id\n          if (this.root.playbackRate) {\n            ctx.playbackRate(this.root.playbackRate)\n          }\n          this.root._videos.push(ctx)\n        }\n      }\n      // #endif\n    },\n\n    /**\n     * @description 图片点击事件\n     * @param {Event} e\n     */\n    imgTap (e) {\n      const node = this.childs[e.currentTarget.dataset.i]\n      if (node.a) {\n        this.linkTap(node.a)\n        return\n      }\n      if (node.attrs.ignore) return\n      // #ifdef H5 || APP-PLUS\n      node.attrs.src = node.attrs.src || node.attrs['data-src']\n      // #endif\n      this.root.$emit('imgtap', node.attrs)\n      // 自动预览图片\n      if (this.root.previewImg) {\n        uni.previewImage({\n          // #ifdef MP-WEIXIN\n          showmenu: this.root.showImgMenu,\n          // #endif\n          // #ifdef MP-ALIPAY\n          enablesavephoto: this.root.showImgMenu,\n          enableShowPhotoDownload: this.root.showImgMenu,\n          // #endif\n          current: parseInt(node.attrs.i),\n          urls: this.root.imgList\n        })\n      }\n    },\n\n    /**\n     * @description 图片长按\n     */\n    imgLongTap (e) {\n      // #ifdef APP-PLUS\n      const attrs = this.childs[e.currentTarget.dataset.i].attrs\n      if (this.opts[3] && !attrs.ignore) {\n        uni.showActionSheet({\n          itemList: ['保存图片'],\n          success: () => {\n            const save = path => {\n              uni.saveImageToPhotosAlbum({\n                filePath: path,\n                success () {\n                  uni.showToast({\n                    title: '保存成功'\n                  })\n                }\n              })\n            }\n            if (this.root.imgList[attrs.i].startsWith('http')) {\n              uni.downloadFile({\n                url: this.root.imgList[attrs.i],\n                success: res => save(res.tempFilePath)\n              })\n            } else {\n              save(this.root.imgList[attrs.i])\n            }\n          }\n        })\n      }\n      // #endif\n    },\n\n    /**\n     * @description 图片加载完成事件\n     * @param {Event} e\n     */\n    imgLoad (e) {\n      const i = e.currentTarget.dataset.i\n      /* #ifndef H5 || (APP-PLUS && VUE2) */\n      if (!this.childs[i].w) {\n        // 设置原宽度\n        this.$set(this.ctrl, i, e.detail.width)\n      } else /* #endif */ if ((this.opts[1] && !this.ctrl[i]) || this.ctrl[i] === -1) {\n        // 加载完毕，取消加载中占位图\n        this.$set(this.ctrl, i, 1)\n      }\n      this.checkReady()\n    },\n\n    /**\n     * @description 检查是否所有图片加载完毕\n     */\n    checkReady () {\n      if (this.root && !this.root.lazyLoad) {\n        this.root._unloadimgs -= 1\n        if (!this.root._unloadimgs) {\n          setTimeout(() => {\n            this.root.getRect().then(rect => {\n              this.root.$emit('ready', rect)\n            }).catch(() => {\n              this.root.$emit('ready', {})\n            })\n          }, 350)\n        }\n      }\n    },\n\n    /**\n     * @description 链接点击事件\n     * @param {Event} e\n     */\n    linkTap (e) {\n      const node = e.currentTarget ? this.childs[e.currentTarget.dataset.i] : {}\n      const attrs = node.attrs || e\n      const href = attrs.href\n      this.root.$emit('linktap', Object.assign({\n        innerText: this.root.getText(node.children || []) // 链接内的文本内容\n      }, attrs))\n      if (href) {\n        if (href[0] === '#') {\n          // 跳转锚点\n          this.root.navigateTo(href.substring(1)).catch(() => { })\n        } else if (href.split('?')[0].includes('://')) {\n          // 复制外部链接\n          if (this.root.copyLink) {\n            // #ifdef H5\n            window.open(href)\n            // #endif\n            // #ifdef MP\n            uni.setClipboardData({\n              data: href,\n              success: () =>\n                uni.showToast({\n                  title: '链接已复制'\n                })\n            })\n            // #endif\n            // #ifdef APP-PLUS\n            plus.runtime.openWeb(href)\n            // #endif\n          }\n        } else {\n          // 跳转页面\n          uni.navigateTo({\n            url: href,\n            fail () {\n              uni.switchTab({\n                url: href,\n                fail () { }\n              })\n            }\n          })\n        }\n      }\n    },\n\n    /**\n     * @description 错误事件\n     * @param {Event} e\n     */\n    mediaError (e) {\n      const i = e.currentTarget.dataset.i\n      const node = this.childs[i]\n      // 加载其他源\n      if (node.name === 'video' || node.name === 'audio') {\n        let index = (this.ctrl[i] || 0) + 1\n        if (index > node.src.length) {\n          index = 0\n        }\n        if (index < node.src.length) {\n          this.$set(this.ctrl, i, index)\n          return\n        }\n      } else if (node.name === 'img') {\n        // #ifdef H5 && VUE3\n        if (this.opts[0] && !this.ctrl.load) return\n        // #endif\n        // 显示错误占位图\n        if (this.opts[2]) {\n          this.$set(this.ctrl, i, -1)\n        }\n        this.checkReady()\n      }\n      if (this.root) {\n        this.root.$emit('error', {\n          source: node.name,\n          attrs: node.attrs,\n          // #ifndef H5 && VUE3\n          errMsg: e.detail.errMsg\n          // #endif\n        })\n      }\n    }\n  }\n}\n</script>\n<style>\n/* a 标签默认效果 */\n._a {\n  padding: 1.5px 0 1.5px 0;\n  color: #366092;\n  word-break: break-all;\n}\n\n/* a 标签点击态效果 */\n._hover {\n  text-decoration: underline;\n  opacity: 0.7;\n}\n\n/* 图片默认效果 */\n._img {\n  max-width: 100%;\n  -webkit-touch-callout: none;\n}\n\n/* 内部样式 */\n\n._block {\n  display: block;\n}\n\n._b,\n._strong {\n  font-weight: bold;\n}\n\n._code {\n  font-family: monospace;\n}\n\n._del {\n  text-decoration: line-through;\n}\n\n._em,\n._i {\n  font-style: italic;\n}\n\n._h1 {\n  font-size: 2em;\n}\n\n._h2 {\n  font-size: 1.5em;\n}\n\n._h3 {\n  font-size: 1.17em;\n}\n\n._h5 {\n  font-size: 0.83em;\n}\n\n._h6 {\n  font-size: 0.67em;\n}\n\n._h1,\n._h2,\n._h3,\n._h4,\n._h5,\n._h6 {\n  display: block;\n  font-weight: bold;\n}\n\n._image {\n  height: 1px;\n}\n\n._ins {\n  text-decoration: underline;\n}\n\n._li {\n  display: list-item;\n}\n\n._ol {\n  list-style-type: decimal;\n}\n\n._ol,\n._ul {\n  display: block;\n  padding-left: 40px;\n  margin: 1em 0;\n}\n\n._q::before {\n  content: '\"';\n}\n\n._q::after {\n  content: '\"';\n}\n\n._sub {\n  font-size: smaller;\n  vertical-align: sub;\n}\n\n._sup {\n  font-size: smaller;\n  vertical-align: super;\n}\n\n._thead,\n._tbody,\n._tfoot {\n  display: table-row-group;\n}\n\n._tr {\n  display: table-row;\n}\n\n._td,\n._th {\n  display: table-cell;\n  vertical-align: middle;\n}\n\n._th {\n  font-weight: bold;\n  text-align: center;\n}\n\n._ul {\n  list-style-type: disc;\n}\n\n._ul ._ul {\n  margin: 0;\n  list-style-type: circle;\n}\n\n._ul ._ul ._ul {\n  list-style-type: square;\n}\n\n._abbr,\n._b,\n._code,\n._del,\n._em,\n._i,\n._ins,\n._label,\n._q,\n._span,\n._strong,\n._sub,\n._sup {\n  display: inline;\n}\n\n/* #ifdef APP-PLUS */\n._video {\n  width: 300px;\n  height: 225px;\n}\n/* #endif */\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./node.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./node.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699050875\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./node.vue?vue&type=custom&index=0&blockType=script&module=handler&lang=wxs\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./node.vue?vue&type=custom&index=0&blockType=script&module=handler&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       \n     }"], "sourceRoot": ""}