{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-input/u-input.vue?471a", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-input/u-input.vue?aa93", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-input/u-input.vue?af5d", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-input/u-input.vue?4860", "uni-app:///uni_modules/uview-ui/components/u-input/u-input.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-input/u-input.vue?1f69", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-input/u-input.vue?f2e5"], "names": ["name", "mixins", "props", "value", "type", "default", "inputAlign", "placeholder", "disabled", "maxlength", "placeholder<PERSON><PERSON><PERSON>", "confirmType", "customStyle", "fixed", "focus", "passwordIcon", "border", "borderColor", "autoHeight", "selectOpen", "height", "clearable", "cursorSpacing", "selectionStart", "selectionEnd", "trim", "showConfirmbar", "adjustPosition", "data", "defaultValue", "inputHeight", "textareaHeight", "validateState", "focused", "showPassword", "lastValue", "watch", "detail", "computed", "inputMaxlength", "getStyle", "style", "getCursorSpacing", "uSelectionStart", "uSelectionEnd", "created", "methods", "handleInput", "setTimeout", "handleBlur", "onFormItemError", "onFocus", "onConfirm", "onClear", "inputClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAimB,CAAgB,2nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC4ErnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,gBA0BA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;QACA;MACA;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;IACA;EACA;EACAuB;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAjC;MACA;MACA;MACA;QACAkC;UACAlC;QACA;MACA;IACA;EACA;EACAmC;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC,gFACA;MACAA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;;QAKA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACAD;QACA;MACA;MACA;MACA;MACAA;QACA;;QAKA;QACA;MACA;IACA;IACAE;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACtVA;AAAA;AAAA;AAAA;AAAoqC,CAAgB,0oCAAG,EAAC,C;;;;;;;;;;;ACAxrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-input/u-input.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-input.vue?vue&type=template&id=113bc24f&scoped=true&\"\nvar renderjs\nimport script from \"./u-input.vue?vue&type=script&lang=js&\"\nexport * from \"./u-input.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-input.vue?vue&type=style&index=0&id=113bc24f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"113bc24f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-input/u-input.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-input.vue?vue&type=template&id=113bc24f&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.type == \"textarea\" ? _vm.__get_style([_vm.getStyle]) : null\n  var s1 = !(_vm.type == \"textarea\") ? _vm.__get_style([_vm.getStyle]) : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showPassword = !_vm.showPassword\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-input.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-input.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t\tclass=\"u-input\"\n\t\t:class=\"{\n\t\t\t'u-input--border': border,\n\t\t\t'u-input--error': validateState\n\t\t}\"\n\t\t:style=\"{\n\t\t\tpadding: `0 ${border ? 20 : 0}rpx`,\n\t\t\tborderColor: borderColor,\n\t\t\ttextAlign: inputAlign\n\t\t}\"\n\t\**********=\"inputClick\"\n\t>\n\t\t<textarea\n\t\t\tv-if=\"type == 'textarea'\"\n\t\t\tclass=\"u-input__input u-input__textarea\"\n\t\t\t:style=\"[getStyle]\"\n\t\t\t:value=\"defaultValue\"\n\t\t\t:placeholder=\"placeholder\"\n\t\t\t:placeholderStyle=\"placeholderStyle\"\n\t\t\t:disabled=\"disabled\"\n\t\t\t:maxlength=\"inputMaxlength\"\n\t\t\t:fixed=\"fixed\"\n\t\t\t:focus=\"focus\"\n\t\t\t:autoHeight=\"autoHeight\"\n\t\t\t:selection-end=\"uSelectionEnd\"\n\t\t\t:selection-start=\"uSelectionStart\"\n\t\t\t:cursor-spacing=\"getCursorSpacing\"\n\t\t\t:show-confirm-bar=\"showConfirmbar\"\n      :adjust-position=\"adjustPosition\"\n\t\t\t@input=\"handleInput\"\n\t\t\t@blur=\"handleBlur\"\n\t\t\t@focus=\"onFocus\"\n\t\t\t@confirm=\"onConfirm\"\n\t\t/>\n\t\t<input\n\t\t\tv-else\n\t\t\tclass=\"u-input__input\"\n\t\t\t:type=\"type == 'password' ? 'text' : type\"\n\t\t\t:style=\"[getStyle]\"\n\t\t\t:value=\"defaultValue\"\n\t\t\t:password=\"type == 'password' && !showPassword\"\n\t\t\t:placeholder=\"placeholder\"\n\t\t\t:placeholderStyle=\"placeholderStyle\"\n\t\t\t:disabled=\"disabled || type === 'select'\"\n\t\t\t:maxlength=\"inputMaxlength\"\n\t\t\t:focus=\"focus\"\n\t\t\t:confirmType=\"confirmType\"\n\t\t\t:cursor-spacing=\"getCursorSpacing\"\n\t\t\t:selection-end=\"uSelectionEnd\"\n\t\t\t:selection-start=\"uSelectionStart\"\n\t\t\t:show-confirm-bar=\"showConfirmbar\"\n\t\t\t:adjust-position=\"adjustPosition\"\n\t\t\t@focus=\"onFocus\"\n\t\t\t@blur=\"handleBlur\"\n\t\t\t@input=\"handleInput\"\n\t\t\t@confirm=\"onConfirm\"\n\t\t/>\n\t\t<view class=\"u-input__right-icon u-flex\">\n\t\t\t<view class=\"u-input__right-icon__clear u-input__right-icon__item\" @tap=\"onClear\" v-if=\"clearable && value != '' && focused\">\n\t\t\t\t<u-icon size=\"32\" name=\"close-circle-fill\" color=\"#c0c4cc\"/>\n\t\t\t</view>\n\t\t\t<view class=\"u-input__right-icon__clear u-input__right-icon__item\" v-if=\"passwordIcon && type == 'password'\">\n\t\t\t\t<u-icon size=\"32\" :name=\"!showPassword ? 'eye' : 'eye-fill'\" color=\"#c0c4cc\" @click=\"showPassword = !showPassword\"/>\n\t\t\t</view>\n\t\t\t<view class=\"u-input__right-icon--select u-input__right-icon__item\" v-if=\"type == 'select'\" :class=\"{\n\t\t\t\t'u-input__right-icon--select--reverse': selectOpen\n\t\t\t}\">\n\t\t\t\t<u-icon name=\"arrow-down-fill\" size=\"26\" color=\"#c0c4cc\"></u-icon>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport Emitter from '../../libs/util/emitter.js';\n\n/**\n * input 输入框\n * @description 此组件为一个输入框，默认没有边框和样式，是专门为配合表单组件u-form而设计的，利用它可以快速实现表单验证，输入内容，下拉选择等功能。\n * @tutorial http://uviewui.com/components/input.html\n * @property {String} type 模式选择，见官网说明\n * @property {Boolean} clearable 是否显示右侧的清除图标(默认true)\n * @property {} v-model 用于双向绑定输入框的值\n * @property {String} input-align 输入框文字的对齐方式(默认left)\n * @property {String} placeholder placeholder显示值(默认 '请输入内容')\n * @property {Boolean} disabled 是否禁用输入框(默认false)\n * @property {String Number} maxlength 输入框的最大可输入长度(默认140)\n * @property {String Number} selection-start 光标起始位置，自动聚焦时有效，需与selection-end搭配使用（默认-1）\n * @property {String Number} maxlength 光标结束位置，自动聚焦时有效，需与selection-start搭配使用（默认-1）\n * @property {String Number} cursor-spacing 指定光标与键盘的距离，单位px(默认0)\n * @property {String} placeholderStyle placeholder的样式，字符串形式，如\"color: red;\"(默认 \"color: #c0c4cc;\")\n * @property {String} confirm-type 设置键盘右下角按钮的文字，仅在type为text时生效(默认done)\n * @property {Object} custom-style 自定义输入框的样式，对象形式\n * @property {Boolean} focus 是否自动获得焦点(默认false)\n * @property {Boolean} fixed 如果type为textarea，且在一个\"position:fixed\"的区域，需要指明为true(默认false)\n * @property {Boolean} password-icon type为password时，是否显示右侧的密码查看图标(默认true)\n * @property {Boolean} border 是否显示边框(默认false)\n * @property {String} border-color 输入框的边框颜色(默认#dcdfe6)\n * @property {Boolean} auto-height 是否自动增高输入区域，type为textarea时有效(默认true)\n * @property {String Number} height 高度，单位rpx(text类型时为70，textarea时为100)\n * @example <u-input v-model=\"value\" :type=\"type\" :border=\"border\" />\n */\nexport default {\n\tname: 'u-input',\n\tmixins: [Emitter],\n\tprops: {\n\t\tvalue: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: ''\n\t\t},\n\t\t// 输入框的类型，textarea，text，number\n\t\ttype: {\n\t\t\ttype: String,\n\t\t\tdefault: 'text'\n\t\t},\n\t\tinputAlign: {\n\t\t\ttype: String,\n\t\t\tdefault: 'left'\n\t\t},\n\t\tplaceholder: {\n\t\t\ttype: String,\n\t\t\tdefault: '请输入内容'\n\t\t},\n\t\tdisabled: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tmaxlength: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 140\n\t\t},\n\t\tplaceholderStyle: {\n\t\t\ttype: String,\n\t\t\tdefault: 'color: #c0c4cc;'\n\t\t},\n\t\tconfirmType: {\n\t\t\ttype: String,\n\t\t\tdefault: 'done'\n\t\t},\n\t\t// 输入框的自定义样式\n\t\tcustomStyle: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {};\n\t\t\t}\n\t\t},\n\t\t// 如果 textarea 是在一个 position:fixed 的区域，需要显示指定属性 fixed 为 true\n\t\tfixed: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 是否自动获得焦点\n\t\tfocus: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 密码类型时，是否显示右侧的密码图标\n\t\tpasswordIcon: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// input|textarea是否显示边框\n\t\tborder: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 输入框的边框颜色\n\t\tborderColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#dcdfe6'\n\t\t},\n\t\tautoHeight: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// type=select时，旋转右侧的图标，标识当前处于打开还是关闭select的状态\n\t\t// open-打开，close-关闭\n\t\tselectOpen: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 高度，单位rpx\n\t\theight: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: ''\n\t\t},\n\t\t// 是否可清空\n\t\tclearable: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 指定光标与键盘的距离，单位 px\n\t\tcursorSpacing: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 0\n\t\t},\n\t\t// 光标起始位置，自动聚焦时有效，需与selection-end搭配使用\n\t\tselectionStart: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: -1\n\t\t},\n\t\t// 光标结束位置，自动聚焦时有效，需与selection-start搭配使用\n\t\tselectionEnd: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: -1\n\t\t},\n\t\t// 是否自动去除两端的空格\n\t\ttrim: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 是否显示键盘上方带有”完成“按钮那一栏\n\t\tshowConfirmbar:{\n\t\t\ttype:Boolean,\n\t\t\tdefault:true\n\t\t},\n\t\t// 弹出键盘时是否自动调节高度，uni-app默认值是true\n\t\tadjustPosition: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tdefaultValue: this.value,\n\t\t\tinputHeight: 70, // input的高度\n\t\t\ttextareaHeight: 100, // textarea的高度\n\t\t\tvalidateState: false, // 当前input的验证状态，用于错误时，边框是否改为红色\n\t\t\tfocused: false, // 当前是否处于获得焦点的状态\n\t\t\tshowPassword: false, // 是否预览密码\n\t\t\tlastValue: '', // 用于头条小程序，判断@input中，前后的值是否发生了变化，因为头条中文下，按下键没有输入内容，也会触发@input时间\n\t\t};\n\t},\n\twatch: {\n\t\tvalue(nVal, oVal) {\n\t\t\tthis.defaultValue = nVal;\n\t\t\t// 当值发生变化，且为select类型时(此时input被设置为disabled，不会触发@input事件)，模拟触发@input事件\n\t\t\tif(nVal != oVal && this.type == 'select') this.handleInput({\n\t\t\t\tdetail: {\n\t\t\t\t\tvalue: nVal\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t},\n\tcomputed: {\n\t\t// 因为uniapp的input组件的maxlength组件必须要数值，这里转为数值，给用户可以传入字符串数值\n\t\tinputMaxlength() {\n\t\t\treturn Number(this.maxlength);\n\t\t},\n\t\tgetStyle() {\n\t\t\tlet style = {};\n\t\t\t// 如果没有自定义高度，就根据type为input还是textarea来分配一个默认的高度\n\t\t\tstyle.minHeight = this.height ? this.height + 'rpx' : this.type == 'textarea' ?\n\t\t\t\tthis.textareaHeight + 'rpx' : this.inputHeight + 'rpx';\n\t\t\tstyle = Object.assign(style, this.customStyle);\n\t\t\treturn style;\n\t\t},\n\t\t//\n\t\tgetCursorSpacing() {\n\t\t\treturn Number(this.cursorSpacing);\n\t\t},\n\t\t// 光标起始位置\n\t\tuSelectionStart() {\n\t\t\treturn String(this.selectionStart);\n\t\t},\n\t\t// 光标结束位置\n\t\tuSelectionEnd() {\n\t\t\treturn String(this.selectionEnd);\n\t\t}\n\t},\n\tcreated() {\n\t\t// 监听u-form-item发出的错误事件，将输入框边框变红色\n\t\tthis.$on('on-form-item-error', this.onFormItemError);\n\t},\n\tmethods: {\n\t\t/**\n\t\t * change 事件\n\t\t * @param event\n\t\t */\n\t\thandleInput(event) {\n\t\t\tlet value = event.detail.value;\n\t\t\t// 判断是否去除空格\n\t\t\tif(this.trim) value = this.$u.trim(value);\n\t\t\t// vue 原生的方法 return 出去\n\t\t\tthis.$emit('input', value);\n\t\t\t// 当前model 赋值\n\t\t\tthis.defaultValue = value;\n\t\t\t// 过一个生命周期再发送事件给u-form-item，否则this.$emit('input')更新了父组件的值，但是微信小程序上\n\t\t\t// 尚未更新到u-form-item，导致获取的值为空，从而校验混论\n\t\t\t// 这里不能延时时间太短，或者使用this.$nextTick，否则在头条上，会造成混乱\n\t\t\tsetTimeout(() => {\n\t\t\t\t// 头条小程序由于自身bug，导致中文下，每按下一个键(尚未完成输入)，都会触发一次@input，导致错误，这里进行判断处理\n\t\t\t\t// #ifdef MP-TOUTIAO\n\t\t\t\tif(this.$u.trim(value) == this.lastValue) return ;\n\t\t\t\tthis.lastValue = value;\n\t\t\t\t// #endif\n\t\t\t\t// 将当前的值发送到 u-form-item 进行校验\n\t\t\t\tthis.dispatch('u-form-item', 'on-form-change', value);\n\t\t\t}, 40)\n\t\t},\n\t\t/**\n\t\t * blur 事件\n\t\t * @param event\n\t\t */\n\t\thandleBlur(event) {\n\t\t\t// 最开始使用的是监听图标@touchstart事件，自从hx2.8.4后，此方法在微信小程序出错\n\t\t\t// 这里改为监听点击事件，手点击清除图标时，同时也发生了@blur事件，导致图标消失而无法点击，这里做一个延时\n\t\t\tlet value = event.detail.value;\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.focused = false;\n\t\t\t}, 100)\n\t\t\t// vue 原生的方法 return 出去\n\t\t\tthis.$emit('blur', value);\n\t\t\tsetTimeout(() => {\n\t\t\t\t// 头条小程序由于自身bug，导致中文下，每按下一个键(尚未完成输入)，都会触发一次@input，导致错误，这里进行判断处理\n\t\t\t\t// #ifdef MP-TOUTIAO\n\t\t\t\tif(this.$u.trim(value) == this.lastValue) return ;\n\t\t\t\tthis.lastValue = value;\n\t\t\t\t// #endif\n\t\t\t\t// 将当前的值发送到 u-form-item 进行校验\n\t\t\t\tthis.dispatch('u-form-item', 'on-form-blur', value);\n\t\t\t}, 40)\n\t\t},\n\t\tonFormItemError(status) {\n\t\t\tthis.validateState = status;\n\t\t},\n\t\tonFocus(event) {\n\t\t\tthis.focused = true;\n\t\t\tthis.$emit('focus');\n\t\t},\n\t\tonConfirm(e) {\n\t\t\tthis.$emit('confirm', e.detail.value);\n\t\t},\n\t\tonClear(event) {\n\t\t\tthis.$emit('input', '');\n\t\t},\n\t\tinputClick() {\n\t\t\tthis.$emit('click');\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/style.components.scss\";\n\n.u-input {\n\tposition: relative;\n\tflex: 1;\n\t@include vue-flex;\n\n\t&__input {\n\t\t//height: $u-form-item-height;\n\t\tfont-size: 28rpx;\n\t\tcolor: $u-main-color;\n\t\tflex: 1;\n\t}\n\n\t&__textarea {\n\t\twidth: auto;\n\t\tfont-size: 28rpx;\n\t\tcolor: $u-main-color;\n\t\tpadding: 10rpx 0;\n\t\tline-height: normal;\n\t\tflex: 1;\n\t}\n\n\t&--border {\n\t\tborder-radius: 6rpx;\n\t\tborder-radius: 4px;\n\t\tborder: 1px solid $u-form-item-border-color;\n\t}\n\n\t&--error {\n\t\tborder-color: $u-type-error!important;\n\t}\n\n\t&__right-icon {\n\n\t\t&__item {\n\t\t\tmargin-left: 10rpx;\n\t\t}\n\n\t\t&--select {\n\t\t\ttransition: transform .4s;\n\n\t\t\t&--reverse {\n\t\t\t\ttransform: rotate(-180deg);\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-input.vue?vue&type=style&index=0&id=113bc24f&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-input.vue?vue&type=style&index=0&id=113bc24f&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753699051759\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}