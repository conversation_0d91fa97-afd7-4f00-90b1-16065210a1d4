/*
 * <AUTHOR> LQ
 * @Description  :
 * @version      : 1.0
 * @Date         : 2021-08-20 16:44:21
 * @LastAuthor   : LQ
 * @lastTime     : 2021-08-20 17:24:32
 * @FilePath     : /u-view2.0/uview-ui/libs/config/props/textarea.js
 */
export default {
	// textarea 组件
	textarea: {
		value: '',
		placeholder: '',
		placeholderClass: 'textarea-placeholder',
		placeholderStyle: 'color: #c0c4cc',
		height: 70,
		confirmType: 'done',
		disabled: false,
		count: false,
		focus: false,
		autoHeight: false,
		fixed: false,
		cursorSpacing: 0,
		cursor: '',
		showConfirmBar: true,
		selectionStart: -1,
		selectionEnd: -1,
		adjustPosition: true,
		disableDefaultPadding: false,
		holdKeyboard: false,
		maxlength: 140,
		border: 'surround',
		formatter: null
	}
}
